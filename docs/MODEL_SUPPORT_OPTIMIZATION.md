# 大模型支持优化方案

## 问题描述

原有的 LLM 服务架构中，模型支持列表限制过于严格，每个提供商都硬编码了支持的模型列表。当供应商发布新模型时（如 `gemini-2.5-flash`），会出现 `MODEL_NOT_SUPPORTED` 错误，需要修改代码才能使用新模型。

## 解决方案

### 1. 引入灵活的模型验证机制

添加了 `strict_model_validation` 配置选项，允许用户在严格验证和宽松验证之间选择：

- **严格模式**（默认）：只允许使用预定义支持列表中的模型
- **宽松模式**：允许使用任何模型名称，仅记录警告

### 2. 配置选项

在 `config.toml` 中添加：

```toml
[app]
    # 模型验证模式配置
    # true: 严格模式，只允许使用预定义支持列表中的模型（默认）
    # false: 宽松模式，允许使用任何模型名称，仅记录警告
    strict_model_validation = true
```

### 3. 代码修改

#### 3.1 基础提供商类 (`app/services/llm/base.py`)

- 重构了 `_validate_config()` 方法
- 添加了 `_validate_model_support()` 方法
- 根据配置选择验证策略

#### 3.2 模型支持列表更新

更新了以下提供商的支持模型列表，添加了 `gemini-2.5-flash`：

- `GeminiVisionProvider`
- `GeminiTextProvider` 
- `GeminiOpenAIVisionProvider`
- `GeminiOpenAITextProvider`

#### 3.3 配置验证器 (`app/services/llm/config_validator.py`)

- 更新了示例模型列表
- 添加了配置建议

## 验证结果

### 严格模式测试

```bash
当前配置的文本模型: gemini-3.0-ultra
严格验证模式: True
错误: [CONFIGURATION_ERROR] 配置错误: 创建提供商实例失败: [MODEL_NOT_SUPPORTED] 供应商 gemini 不支持模型 gemini-3.0-ultra
```

### 宽松模式测试

```bash
当前配置的文本模型: gemini-3.0-ultra
严格验证模式: False
WARNING | 模型 gemini-3.0-ultra 未在供应商 gemini 的预定义支持列表中，但已启用宽松验证模式。支持的模型列表: ['gemini-2.5-flash', 'gemini-2.0-flash-lite', 'gemini-2.0-flash', 'gemini-1.5-pro', 'gemini-1.5-flash']
成功创建提供商: gemini - gemini-3.0-ultra
```

### 原问题解决

原始错误：
```
ERROR | 创建文本模型提供商实例失败: gemini - [MODEL_NOT_SUPPORTED] 供应商 gemini 不支持模型 gemini-2.5-flash
```

现在已解决，`gemini-2.5-flash` 可以正常使用。

## 优势

1. **向后兼容**：默认保持严格模式，确保现有代码不受影响
2. **灵活性**：宽松模式允许快速适应新模型发布
3. **安全性**：提供警告信息，让用户了解使用未验证模型的风险
4. **可维护性**：减少因新模型发布而需要频繁修改代码的问题

## 使用建议

1. **生产环境**：建议使用严格模式，确保稳定性
2. **开发测试**：可以使用宽松模式测试新模型
3. **新模型适配**：
   - 临时使用宽松模式快速验证新模型
   - 确认稳定后更新代码中的支持列表
   - 恢复严格模式

## 相关文档

- [模型验证配置说明](MODEL_VALIDATION_CONFIG.md)
- [LLM 迁移指南](LLM_MIGRATION_GUIDE.md)
