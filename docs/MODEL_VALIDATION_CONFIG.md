# 模型验证配置说明

## 概述

为了解决大模型更新频繁导致的模型支持问题，我们引入了灵活的模型验证机制。该机制允许用户在严格验证和宽松验证之间进行选择。

## 配置选项

### strict_model_validation

在 `config.toml` 文件的 `[app]` 部分添加以下配置：

```toml
[app]
    # 模型验证模式配置
    # true: 严格模式，只允许使用预定义支持列表中的模型（默认）
    # false: 宽松模式，允许使用任何模型名称，仅记录警告
    strict_model_validation = true
```

## 验证模式说明

### 严格模式（默认）
- **配置值**: `strict_model_validation = true`
- **行为**: 只允许使用预定义支持列表中的模型
- **错误处理**: 如果使用未支持的模型，会抛出 `MODEL_NOT_SUPPORTED` 异常
- **适用场景**: 生产环境，需要确保模型兼容性

### 宽松模式
- **配置值**: `strict_model_validation = false`
- **行为**: 允许使用任何模型名称
- **错误处理**: 如果使用未在预定义列表中的模型，仅记录警告日志
- **适用场景**: 测试新模型，或者当新模型发布但代码尚未更新时

## 使用场景

### 场景1：新模型发布
当供应商发布新模型（如 `gemini-2.5-flash`）但代码中的支持列表尚未更新时：

1. **临时解决方案**：设置 `strict_model_validation = false`
2. **配置模型**：在配置文件中使用新模型名称
3. **观察日志**：查看警告信息，确认模型可以正常工作

### 场景2：测试实验性模型
当需要测试供应商的实验性或预览版模型时：

1. 启用宽松模式
2. 配置实验性模型名称
3. 进行功能测试

## 日志信息

### 严格模式错误日志
```
ERROR | 创建文本模型提供商实例失败: gemini - [MODEL_NOT_SUPPORTED] 供应商 gemini 不支持模型 gemini-2.5-flash
```

### 宽松模式警告日志
```
WARNING | 模型 gemini-2.5-flash 未在供应商 gemini 的预定义支持列表中，但已启用宽松验证模式。支持的模型列表: ['gemini-2.0-flash-lite', 'gemini-2.0-flash', 'gemini-1.5-pro', 'gemini-1.5-flash']
```

## 最佳实践

1. **生产环境**：建议使用严格模式，确保稳定性
2. **开发测试**：可以使用宽松模式测试新模型
3. **模型更新**：当确认新模型稳定后，应该更新代码中的支持列表
4. **监控日志**：在宽松模式下，注意观察警告日志

## 支持的供应商

目前支持以下供应商的模型验证配置：
- Gemini (原生API)
- Gemini (OpenAI兼容API)
- OpenAI
- DeepSeek
- Qwen
- SiliconFlow

## 故障排除

### 问题：MODEL_NOT_SUPPORTED 错误
**解决方案**：
1. 检查模型名称是否正确
2. 如果是新模型，设置 `strict_model_validation = false`
3. 查看日志确认模型是否可以正常工作

### 问题：宽松模式下模型调用失败
**解决方案**：
1. 检查API密钥和base_url配置
2. 确认供应商确实支持该模型
3. 查看API响应错误信息

## 配置示例

```toml
[app]
    # 启用宽松验证模式
    strict_model_validation = false
    
    # 使用新发布的模型
    text_llm_provider = "gemini"
    text_gemini_api_key = "your-api-key"
    text_gemini_model_name = "gemini-2.5-flash"
    text_gemini_base_url = "https://generativelanguage.googleapis.com/v1beta"
```
